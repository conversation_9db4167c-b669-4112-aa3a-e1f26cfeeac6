package com.xyy.saas.inquiry.common.api.rational;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.common.api.rational.dto.LimitDrugCatalogDto;
import com.xyy.saas.inquiry.common.api.rational.dto.LimitDrugCatalogSaveDto;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.LimitDrugCatalogOperationVo;

/**
 * 目录限制用药
 *
 * <AUTHOR>
 * @Date 4/25/24 3:07 PM
 */
public interface LimitDrugCatalogApi {

    /**
     * 保存
     *
     * @param limitDrugCatalogSaveDto
     * @return
     */
    CommonResult<Boolean> save(LimitDrugCatalogSaveDto limitDrugCatalogSaveDto);

    /**
     * 分页查询
     *
     * @param limitDrugCatalogOperationDto
     * @return
     */
    CommonResult<PageResult<LimitDrugCatalogDto>> pageQuery(LimitDrugCatalogOperationVo limitDrugCatalogOperationDto);

    /**
     * 删除
     *
     * @param limitDrugCatalogOperationDto
     * @return
     */
    CommonResult<Boolean> deleteByIdList(LimitDrugCatalogOperationVo limitDrugCatalogOperationDto);

    /**
     * 查询配置
     *
     * @return
     */
    CommonResult<LimitDrugCatalogOperationVo> getConfig();

    /**
     * 新增或修改配置
     *
     * @param limitDrugCatalogOperationVo
     * @return
     */
    CommonResult<Boolean> saveOrUpdateConfig(LimitDrugCatalogOperationVo limitDrugCatalogOperationVo);

}
