package com.xyy.saas.inquiry.pharmacist.server.service.pharmacist;

import com.xyy.saas.inquiry.enums.inquiry.MedicineTypeEnum;
import com.xyy.saas.inquiry.enums.system.EnvTagEnum;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionRespDTO;
import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.pharmacist.InquiryPharmacistDO;
import com.xyy.saas.inquiry.pharmacist.server.service.pharmacist.dto.PharmacistReceiverImUserDto;

/**
 * @Author: xucao
 * @DateTime: 2025/4/1 13:51
 * @Description: 药师IM相关服务
 **/
public interface InquiryPharmacistImService {

    /**
     * 发送处方审核消息给患者
     *
     * @param inquiryPref 问诊单号
     */
    void sendPrescriptionAuditMsgToPatient(String inquiryPref);

    /**
     * 发送处方笺绘制完成消息给患者
     *
     * @param inquiryPref 问诊单号
     */
    void sendPrescriptionDrawnFinish(String inquiryPref);

    /**
     * 发起审核处方视频通知到视频接收端用户
     *
     * @param receiverImAccount 问诊单号
     */
    void sendAuditPrescriptionVideo(PharmacistReceiverImUserDto receiverImUserDto, Integer actionType, InquiryPrescriptionRespDTO prescription);

    /**
     * 推送处方待审核语音提醒消息
     *
     * @param headTenantId
     * @param medicineTypeEnum
     */
    void sendPrescriptionWaitAuditToPharmacist(String prescriptionPref, EnvTagEnum envTagEnum, Long headTenantId, MedicineTypeEnum medicineTypeEnum, Integer pharmacistType, Integer pharmacistNature);

    /**
     * 推送新处方消息给药师
     * @param prescriptionPref
     * @param envTagEnum
     * @param tenantId
     * @param medicineTypeEnum
     * @param pharmacistType
     * @param pharmacistNature
     */
    void notifyForNewPrescription(String prescriptionPref, EnvTagEnum envTagEnum, Long tenantId, MedicineTypeEnum medicineTypeEnum, Integer pharmacistType, Integer pharmacistNature);

    /**
     * 推送处方被领取消息给患者
     *
     * @param userId
     * @param inquiryPref
     */
    void sendPrescriptionReceivePatient(Long userId, InquiryPharmacistDO pharmacist, String inquiryPref);

    /**
     * 获取远程审方通话控制接收方IM账号
     *
     * @param prescription
     * @return
     */
    PharmacistReceiverImUserDto getReceiverImAccount(InquiryPrescriptionRespDTO prescription);

    /**
     * 推送处方审核视频通话处理结果给发起方
     *
     * @param receiverImAccount
     * @param handleStatus
     */
    void sendAuditVideoCallHandleResult(PharmacistReceiverImUserDto receiverImUserDto, Integer handleStatus, InquiryPrescriptionRespDTO prescription);
}
