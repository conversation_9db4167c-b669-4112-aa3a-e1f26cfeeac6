package com.xyy.saas.inquiry.im.server.service.trtc.strategy.trtccallback;

import com.alibaba.fastjson.JSON;
import com.xyy.saas.inquiry.enums.tencent.TencentTrtcCallBackEventEnum;
import com.xyy.saas.inquiry.hospital.api.doctor.InquiryDoctorApi;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryDoctorDto;
import com.xyy.saas.inquiry.im.server.controller.app.callback.vo.TencentTrtcCallBackReqVO;
import com.xyy.saas.inquiry.im.server.controller.app.callback.vo.TencentTrtcCallBackReqVO.EventInfo;
import com.xyy.saas.inquiry.im.server.service.tencent.TencentTrtcClient;
import com.xyy.saas.inquiry.patient.api.inquiry.InquiryApi;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Author: xucao
 * @Date: 2025/08/25 21:40
 * @Description: 输入在线媒体流结束回调
 */
@Component
@Slf4j
public class InputStreamEndHandleStrategy extends TencentTrtcCallBackHandleStrategy {

    @Resource
    private InquiryApi inquiryApi;

    @Resource
    private TencentTrtcClient tencentTrtcClient;

    /**
     * 策略执行器
     *
     * @param callBackReqVO 回调参数
     */
    @Override
    public Boolean execute(TencentTrtcCallBackReqVO callBackReqVO) {
        log.info("trtc输入在线媒体流结束回调:{}", JSON.toJSONString(callBackReqVO));
        // 校验参数
        callBackReqVO.checkEvent();
        // 事件参数
        EventInfo eventInfo = callBackReqVO.getEventInfo();
        // 问诊单信息
        InquiryRecordDto inquiryDto = inquiryApi.getInquiryRecord(eventInfo.getRoomId());
        // 真人接诊场景  不处理
        if(inquiryDto.isManualInquiry()){
            return Boolean.FALSE;
        }
        // 自动开方场景，结束推流后解散直播间
        tencentTrtcClient.destroyRoom(inquiryDto.getPref());
        return Boolean.TRUE;
    }

    /**
     * 获取策略对应的事件
     *
     * @return 事件
     */
    @Override
    public TencentTrtcCallBackEventEnum getEvent() {
        return TencentTrtcCallBackEventEnum.EVENT_TYPE_STREAM_INGEST_END;
    }
}
